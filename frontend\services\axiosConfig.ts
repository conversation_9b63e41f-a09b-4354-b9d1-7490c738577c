// services/axiosConfig.ts
import axios from 'axios';

// Create a custom axios instance with default configuration
const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the auth token in all requests
instance.interceptors.request.use(
  (config) => {
    // Get the token from localStorage if available
    // Try multiple possible token keys for compatibility
    const token = typeof window !== 'undefined' ?
      (localStorage.getItem('token') ||
       localStorage.getItem('jwt_token') ||
       localStorage.getItem('auth_token')) : null;

    // If token exists, add it to the Authorization header
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Debug API calls
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
      params: config.params,
      data: config.data,
      timestamp: new Date().toISOString(),
      caller: new Error().stack?.split('\n')[2]?.trim() || 'unknown'
    });

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common response issues
instance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && error.response.status === 401) {
      // Clear all possible token keys and redirect to login if needed
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token');
        localStorage.removeItem('jwt_token');
        localStorage.removeItem('auth_token');
        console.log('Authentication error: Cleared all tokens');
        // Optionally redirect to login page
        // window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

export default instance;
