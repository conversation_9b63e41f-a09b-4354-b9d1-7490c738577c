// components/upload/UploadProvider.tsx
'use client';

import React from 'react';
import { SimplifiedUploadProvider } from '../../contexts/SimplifiedUploadContext';
import UploadModal from './UploadModal';

interface UploadProviderProps {
  children: React.ReactNode;
}

/**
 * Main upload provider component that wraps the app and provides upload functionality
 * This should be added to your main layout or app component
 */
const UploadProvider: React.FC<UploadProviderProps> = ({ children }) => {
  return (
    <SimplifiedUploadProvider>
      {children}
      <UploadModal />
    </SimplifiedUploadProvider>
  );
};

export default UploadProvider;
