// contexts/SimplifiedUploadContext.tsx
'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

export type ContentType = 'moments' | 'photos' | 'videos';
export type UploadStatus = 'idle' | 'uploading' | 'success' | 'error';

interface UploadState {
  isModalOpen: boolean;
  contentType: ContentType | null;
  uploadStatus: UploadStatus;
  uploadProgress: number;
  error: string | null;
  currentFile: File | null;
}

interface UploadContextType {
  state: UploadState;
  openUploadModal: (contentType?: ContentType) => void;
  closeUploadModal: () => void;
  setContentType: (type: ContentType) => void;
  setUploadStatus: (status: UploadStatus) => void;
  setUploadProgress: (progress: number) => void;
  setError: (error: string | null) => void;
  setCurrentFile: (file: File | null) => void;
  resetUpload: () => void;
}

const SimplifiedUploadContext = createContext<UploadContextType | undefined>(undefined);

const initialState: UploadState = {
  isModalOpen: false,
  contentType: null,
  uploadStatus: 'idle',
  uploadProgress: 0,
  error: null,
  currentFile: null
};

interface SimplifiedUploadProviderProps {
  children: ReactNode;
}

export const SimplifiedUploadProvider: React.FC<SimplifiedUploadProviderProps> = ({ children }) => {
  const [state, setState] = useState<UploadState>(initialState);

  const openUploadModal = (contentType?: ContentType) => {
    setState(prev => ({
      ...prev,
      isModalOpen: true,
      contentType: contentType || null,
      uploadStatus: 'idle',
      uploadProgress: 0,
      error: null
    }));
  };

  const closeUploadModal = () => {
    setState(prev => ({
      ...prev,
      isModalOpen: false,
      contentType: null,
      uploadStatus: 'idle',
      uploadProgress: 0,
      error: null,
      currentFile: null
    }));
  };

  const setContentType = (type: ContentType) => {
    setState(prev => ({
      ...prev,
      contentType: type
    }));
  };

  const setUploadStatus = (status: UploadStatus) => {
    setState(prev => ({
      ...prev,
      uploadStatus: status
    }));
  };

  const setUploadProgress = (progress: number) => {
    setState(prev => ({
      ...prev,
      uploadProgress: progress
    }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
      uploadStatus: error ? 'error' : prev.uploadStatus
    }));
  };

  const setCurrentFile = (file: File | null) => {
    setState(prev => ({
      ...prev,
      currentFile: file
    }));
  };

  const resetUpload = () => {
    setState(prev => ({
      ...prev,
      uploadStatus: 'idle',
      uploadProgress: 0,
      error: null,
      currentFile: null
    }));
  };

  const contextValue: UploadContextType = {
    state,
    openUploadModal,
    closeUploadModal,
    setContentType,
    setUploadStatus,
    setUploadProgress,
    setError,
    setCurrentFile,
    resetUpload
  };

  return (
    <SimplifiedUploadContext.Provider value={contextValue}>
      {children}
    </SimplifiedUploadContext.Provider>
  );
};

export const useSimplifiedUpload = (): UploadContextType => {
  const context = useContext(SimplifiedUploadContext);
  if (context === undefined) {
    throw new Error('useSimplifiedUpload must be used within a SimplifiedUploadProvider');
  }
  return context;
};

// Hook for easy upload modal management
export const useUploadModal = () => {
  const { state, openUploadModal, closeUploadModal } = useSimplifiedUpload();
  
  return {
    isOpen: state.isModalOpen,
    open: openUploadModal,
    close: closeUploadModal,
    contentType: state.contentType
  };
};

// Hook for upload progress tracking
export const useUploadProgress = () => {
  const { state, setUploadStatus, setUploadProgress, setError } = useSimplifiedUpload();
  
  return {
    status: state.uploadStatus,
    progress: state.uploadProgress,
    error: state.error,
    setStatus: setUploadStatus,
    setProgress: setUploadProgress,
    setError
  };
};

// Hook for file management
export const useUploadFile = () => {
  const { state, setCurrentFile } = useSimplifiedUpload();
  
  return {
    currentFile: state.currentFile,
    setFile: setCurrentFile
  };
};
