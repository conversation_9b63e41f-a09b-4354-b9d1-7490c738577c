// components/upload/UploadButton.tsx
'use client';

import React from 'react';
import { PlusCircle, Camera, Video, Image } from 'lucide-react';
import { useUploadModal, ContentType } from '../../contexts/SimplifiedUploadContext';

interface UploadButtonProps {
  contentType?: ContentType;
  variant?: 'default' | 'icon' | 'text';
  className?: string;
  children?: React.ReactNode;
}

const UploadButton: React.FC<UploadButtonProps> = ({ 
  contentType, 
  variant = 'default',
  className = '',
  children 
}) => {
  const { open } = useUploadModal();

  const handleClick = () => {
    open(contentType);
  };

  const getIcon = () => {
    if (contentType === 'moments') return <Camera className="w-5 h-5" />;
    if (contentType === 'photos') return <Image className="w-5 h-5" />;
    if (contentType === 'videos') return <Video className="w-5 h-5" />;
    return <PlusCircle className="w-5 h-5" />;
  };

  const getText = () => {
    if (contentType === 'moments') return 'Upload Moment';
    if (contentType === 'photos') return 'Upload Photo';
    if (contentType === 'videos') return 'Upload Video';
    return 'Upload Content';
  };

  const baseClasses = "inline-flex items-center justify-center transition-colors duration-200";
  
  const variantClasses = {
    default: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",
    icon: "p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full",
    text: "text-red-600 hover:text-red-700 underline"
  };

  return (
    <button
      onClick={handleClick}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {variant !== 'text' && (
        <span className={variant === 'default' ? 'mr-2' : ''}>
          {getIcon()}
        </span>
      )}
      {variant !== 'icon' && (children || getText())}
    </button>
  );
};

export default UploadButton;
