// utils/uploadDataHandlers.ts
/**
 * Dedicated data handlers for different upload types
 * Separates data validation and transformation logic for better maintainability
 */

export interface BaseUploadData {
  file: File;
  mediaType: 'photo' | 'video';
  title: string;
}

export interface MomentsUploadData extends BaseUploadData {
  mediaSubtype: 'story';
  isStory: boolean;
  duration?: number;
  thumbnail?: File | null;
}

export interface PhotoUploadData extends BaseUploadData {
  mediaType: 'photo';
  mediaSubtype: 'post';
  caption: string;
  place: string;
  eventType: string;
  tags?: string[];
}

export interface VideoUploadData extends BaseUploadData {
  mediaType: 'video';
  mediaSubtype: 'flash' | 'glimpse' | 'movie';
  caption: string;
  place: string;
  partner: string;
  budget: string;
  weddingStyle: string;
  eventType: string;
  videoCategory: 'my_wedding' | 'wedding_vlog';
  vendorDetails: Record<string, { name: string; mobileNumber: string }>;
  duration: number;
  thumbnail: File | null;
  tags?: string[];
}

export interface UploadValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Data handler for moments (photo and video stories)
 */
export class MomentsDataHandler {
  static validate(data: Partial<MomentsUploadData>): UploadValidationResult {
    const errors: string[] = [];

    if (!data.file) {
      errors.push('File is required');
    }

    if (!data.mediaType) {
      errors.push('Media type is required');
    }

    if (data.mediaType === 'video' && !data.duration) {
      errors.push('Video duration is required');
    }

    // File size validation
    if (data.file) {
      const maxSize = 5000 * 1024 * 1024; // 5GB
      if (data.file.size > maxSize) {
        errors.push('File size exceeds maximum limit of 5GB');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static prepareUploadData(data: Partial<MomentsUploadData>): MomentsUploadData {
    const validation = this.validate(data);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    return {
      file: data.file!,
      mediaType: data.mediaType!,
      mediaSubtype: 'story',
      isStory: true,
      title: data.title || data.file!.name.replace(/\.[^/.]+$/, ""),
      duration: data.duration,
      thumbnail: data.thumbnail || null
    };
  }

  static getApiPayload(data: MomentsUploadData) {
    return {
      file: data.file,
      mediaType: data.mediaType,
      category: data.mediaSubtype,
      title: data.title,
      description: '', // No description for moments
      tags: [], // No tags for moments
      details: { is_story: true }, // Flag for backend
      duration: data.duration,
      thumbnail: data.thumbnail
    };
  }
}

/**
 * Data handler for photo uploads
 */
export class PhotoDataHandler {
  static validate(data: Partial<PhotoUploadData>): UploadValidationResult {
    const errors: string[] = [];

    if (!data.file) {
      errors.push('File is required');
    }

    if (!data.caption?.trim()) {
      errors.push('Caption is required');
    }

    if (!data.place?.trim()) {
      errors.push('Place is required');
    }

    if (!data.eventType?.trim()) {
      errors.push('Event type is required');
    }

    // File size validation
    if (data.file) {
      const maxSize = 5000 * 1024 * 1024; // 5GB
      if (data.file.size > maxSize) {
        errors.push('File size exceeds maximum limit of 5GB');
      }

      // File type validation
      if (!data.file.type.startsWith('image/')) {
        errors.push('File must be an image');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static prepareUploadData(data: Partial<PhotoUploadData>): PhotoUploadData {
    const validation = this.validate(data);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    return {
      file: data.file!,
      mediaType: 'photo',
      mediaSubtype: 'post',
      title: data.title || data.caption!,
      caption: data.caption!.trim(),
      place: data.place!.trim(),
      eventType: data.eventType!.trim(),
      tags: data.tags || []
    };
  }

  static getApiPayload(data: PhotoUploadData) {
    return {
      file: data.file,
      mediaType: data.mediaType,
      category: data.mediaSubtype,
      title: data.title,
      description: data.caption,
      tags: data.tags,
      details: {
        place: data.place,
        event_type: data.eventType
      },
      duration: undefined,
      thumbnail: null
    };
  }
}

/**
 * Data handler for video uploads (flashes, glimpses, movies)
 */
export class VideoDataHandler {
  static validate(data: Partial<VideoUploadData>): UploadValidationResult {
    const errors: string[] = [];

    if (!data.file) {
      errors.push('File is required');
    }

    if (!data.caption?.trim()) {
      errors.push('Caption is required');
    }

    if (!data.place?.trim()) {
      errors.push('Place is required');
    }

    if (!data.partner?.trim()) {
      errors.push('Partner is required');
    }

    if (!data.budget?.trim()) {
      errors.push('Budget is required');
    }

    if (!data.weddingStyle?.trim()) {
      errors.push('Wedding style is required');
    }

    if (!data.eventType?.trim()) {
      errors.push('Event type is required');
    }

    if (!data.videoCategory) {
      errors.push('Video category is required');
    }

    if (!data.duration) {
      errors.push('Video duration is required');
    }

    // Vendor details validation based on category
    if (data.videoCategory === 'my_wedding') {
      const requiredVendors = ['venue', 'photographer', 'makeup_artist', 'decoration'];
      const providedVendors = Object.keys(data.vendorDetails || {});

      for (const vendor of requiredVendors) {
        if (!providedVendors.includes(vendor)) {
          errors.push(`${vendor} vendor details are required for my_wedding category`);
        } else {
          const vendorData = data.vendorDetails![vendor];
          if (!vendorData.name?.trim()) {
            errors.push(`${vendor} name is required`);
          }
          if (!vendorData.mobileNumber?.trim()) {
            errors.push(`${vendor} mobile number is required`);
          }
        }
      }
    } else if (data.videoCategory === 'wedding_vlog') {
      const providedVendors = Object.keys(data.vendorDetails || {});
      if (providedVendors.length === 0) {
        errors.push('At least one vendor is required for wedding_vlog category');
      } else {
        // Validate provided vendor details
        for (const vendor of providedVendors) {
          const vendorData = data.vendorDetails![vendor];
          if (!vendorData.name?.trim()) {
            errors.push(`${vendor} name is required`);
          }
          if (!vendorData.mobileNumber?.trim()) {
            errors.push(`${vendor} mobile number is required`);
          }
        }
      }
    }

    // File size validation
    if (data.file) {
      const maxSize = 5000 * 1024 * 1024; // 5GB
      if (data.file.size > maxSize) {
        errors.push('File size exceeds maximum limit of 5GB');
      }

      // File type validation
      if (!data.file.type.startsWith('video/')) {
        errors.push('File must be a video');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static prepareUploadData(data: Partial<VideoUploadData>): VideoUploadData {
    const validation = this.validate(data);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Determine media subtype based on duration
    let mediaSubtype: 'flash' | 'glimpse' | 'movie' = 'movie';
    if (data.duration! <= 15) {
      mediaSubtype = 'flash';
    } else if (data.duration! <= 60) {
      mediaSubtype = 'glimpse';
    }

    return {
      file: data.file!,
      mediaType: 'video',
      mediaSubtype,
      title: data.title || data.caption!,
      caption: data.caption!.trim(),
      place: data.place!.trim(),
      partner: data.partner!.trim(),
      budget: data.budget!.trim(),
      weddingStyle: data.weddingStyle!.trim(),
      eventType: data.eventType!.trim(),
      videoCategory: data.videoCategory!,
      vendorDetails: data.vendorDetails!,
      duration: data.duration!,
      thumbnail: data.thumbnail || null,
      tags: data.tags || []
    };
  }

  static getApiPayload(data: VideoUploadData) {
    return {
      file: data.file,
      mediaType: data.mediaType,
      category: data.mediaSubtype,
      title: data.title,
      description: data.caption,
      tags: data.tags,
      details: {
        place: data.place,
        partner: data.partner,
        budget: data.budget,
        wedding_style: data.weddingStyle,
        event_type: data.eventType,
        video_category: data.videoCategory,
        vendor_details: data.vendorDetails
      },
      duration: data.duration,
      thumbnail: data.thumbnail
    };
  }
}
