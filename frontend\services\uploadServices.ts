// services/uploadServices.ts
/**
 * Dedicated upload services for different content types
 * Provides clean, type-specific upload functionality
 */

import { uploadService } from './api';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  PhotoDataHandler, 
  VideoDataHandler,
  MomentsUploadData,
  PhotoUploadData,
  VideoUploadData
} from '../utils/uploadDataHandlers';

export interface UploadProgress {
  percentage: number;
  uploadedBytes: number;
  totalBytes: number;
  speed: number; // bytes per second
}

export interface UploadResult {
  success: boolean;
  url?: string;
  mediaId?: string;
  message?: string;
  error?: string;
}

/**
 * Service for uploading moments (photo and video stories)
 */
export class MomentsUploadService {
  static async upload(
    data: Partial<MomentsUploadData>,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      console.log('MOMENTS UPLOAD - Starting upload process');
      
      // Validate and prepare data
      const uploadData = MomentsDataHandler.prepareUploadData(data);
      const apiPayload = MomentsDataHandler.getApiPayload(uploadData);
      
      console.log('MOMENTS UPLOAD - Data prepared:', {
        fileName: uploadData.file.name,
        mediaType: uploadData.mediaType,
        isStory: uploadData.isStory,
        duration: uploadData.duration
      });

      // Upload using the main upload service
      const result = await uploadService.handleUpload(
        apiPayload.file,
        apiPayload.mediaType,
        apiPayload.category,
        apiPayload.title,
        apiPayload.description,
        apiPayload.tags,
        apiPayload.details,
        apiPayload.duration,
        apiPayload.thumbnail,
        (progress) => {
          if (onProgress) {
            onProgress({
              percentage: progress,
              uploadedBytes: (progress / 100) * uploadData.file.size,
              totalBytes: uploadData.file.size,
              speed: 0 // Speed calculation would need to be implemented
            });
          }
        }
      );

      console.log('MOMENTS UPLOAD - Upload completed successfully:', result);

      return {
        success: true,
        url: result.url,
        mediaId: result.media_id,
        message: 'Moment uploaded successfully'
      };

    } catch (error: any) {
      console.error('MOMENTS UPLOAD - Upload failed:', error);
      return {
        success: false,
        error: error.message || 'Upload failed'
      };
    }
  }
}

/**
 * Service for uploading photos
 */
export class PhotoUploadService {
  static async upload(
    data: Partial<PhotoUploadData>,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      console.log('PHOTO UPLOAD - Starting upload process');
      
      // Validate and prepare data
      const uploadData = PhotoDataHandler.prepareUploadData(data);
      const apiPayload = PhotoDataHandler.getApiPayload(uploadData);
      
      console.log('PHOTO UPLOAD - Data prepared:', {
        fileName: uploadData.file.name,
        caption: uploadData.caption,
        place: uploadData.place,
        eventType: uploadData.eventType
      });

      // Upload using the main upload service
      const result = await uploadService.handleUpload(
        apiPayload.file,
        apiPayload.mediaType,
        apiPayload.category,
        apiPayload.title,
        apiPayload.description,
        apiPayload.tags,
        apiPayload.details,
        apiPayload.duration,
        apiPayload.thumbnail,
        (progress) => {
          if (onProgress) {
            onProgress({
              percentage: progress,
              uploadedBytes: (progress / 100) * uploadData.file.size,
              totalBytes: uploadData.file.size,
              speed: 0 // Speed calculation would need to be implemented
            });
          }
        }
      );

      console.log('PHOTO UPLOAD - Upload completed successfully:', result);

      return {
        success: true,
        url: result.url,
        mediaId: result.media_id,
        message: 'Photo uploaded successfully'
      };

    } catch (error: any) {
      console.error('PHOTO UPLOAD - Upload failed:', error);
      return {
        success: false,
        error: error.message || 'Upload failed'
      };
    }
  }
}

/**
 * Service for uploading videos (flashes, glimpses, movies)
 */
export class VideoUploadService {
  static async upload(
    data: Partial<VideoUploadData>,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      console.log('VIDEO UPLOAD - Starting upload process');
      
      // Validate and prepare data
      const uploadData = VideoDataHandler.prepareUploadData(data);
      const apiPayload = VideoDataHandler.getApiPayload(uploadData);
      
      console.log('VIDEO UPLOAD - Data prepared:', {
        fileName: uploadData.file.name,
        mediaSubtype: uploadData.mediaSubtype,
        caption: uploadData.caption,
        videoCategory: uploadData.videoCategory,
        duration: uploadData.duration,
        vendorCount: Object.keys(uploadData.vendorDetails).length
      });

      // Upload using the main upload service
      const result = await uploadService.handleUpload(
        apiPayload.file,
        apiPayload.mediaType,
        apiPayload.category,
        apiPayload.title,
        apiPayload.description,
        apiPayload.tags,
        apiPayload.details,
        apiPayload.duration,
        apiPayload.thumbnail,
        (progress) => {
          if (onProgress) {
            onProgress({
              percentage: progress,
              uploadedBytes: (progress / 100) * uploadData.file.size,
              totalBytes: uploadData.file.size,
              speed: 0 // Speed calculation would need to be implemented
            });
          }
        }
      );

      console.log('VIDEO UPLOAD - Upload completed successfully:', result);

      return {
        success: true,
        url: result.url,
        mediaId: result.media_id,
        message: 'Video uploaded successfully'
      };

    } catch (error: any) {
      console.error('VIDEO UPLOAD - Upload failed:', error);
      return {
        success: false,
        error: error.message || 'Upload failed'
      };
    }
  }
}

/**
 * Factory function to get the appropriate upload service
 */
export function getUploadService(contentType: 'moments' | 'photos' | 'videos') {
  switch (contentType) {
    case 'moments':
      return MomentsUploadService;
    case 'photos':
      return PhotoUploadService;
    case 'videos':
      return VideoUploadService;
    default:
      throw new Error(`Unknown content type: ${contentType}`);
  }
}

/**
 * Unified upload function that automatically determines the service to use
 */
export async function uploadContent(
  contentType: 'moments' | 'photos' | 'videos',
  data: Partial<MomentsUploadData | PhotoUploadData | VideoUploadData>,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  const service = getUploadService(contentType);
  return service.upload(data as any, onProgress);
}
