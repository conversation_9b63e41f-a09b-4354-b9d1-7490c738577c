import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import https from 'https';
import { Readable } from 'stream';

// For Next.js App Router, we need to use this export for the config
export const fetchCache = 'force-no-store';
export const revalidate = 0;
export const dynamic = 'force-dynamic';

// Set the correct runtime for large file handling
export const runtime = 'nodejs'; // Use Node.js runtime instead of Edge for large files

// Constants for optimized uploads
const CHUNK_SIZE = 120 * 1024 * 1024; // 120MB chunks (minimum size as requested)
const LARGE_FILE_THRESHOLD = 10 * 1024 * 1024; // 10MB threshold for optimized uploads
const MEDIUM_FILE_THRESHOLD = 100 * 1024 * 1024; // 100MB threshold for medium files

// Configure axios defaults for better performance
axios.defaults.maxBodyLength = Infinity;
axios.defaults.maxContentLength = Infinity;
axios.defaults.maxRedirects = 5;
axios.defaults.timeout = 3600000; // 1 hour timeout

// This is the correct way to configure large file uploads in App Router
export async function POST(request: NextRequest) {
  try {
    // For direct uploads to S3, we just need the URL and the file
    const url = request.nextUrl.searchParams.get('url');
    const useOptimized = request.nextUrl.searchParams.get('optimized') !== 'false'; // Default to true
    const useChunked = request.nextUrl.searchParams.get('chunked') === 'true';
    const customChunkSize = request.nextUrl.searchParams.get('chunkSize');

    // Use custom chunk size if provided
    const chunkSize = customChunkSize ? parseInt(customChunkSize) : CHUNK_SIZE;

    if (!url) {
      return NextResponse.json(
        { error: 'Missing URL parameter' },
        { status: 400 }
      );
    }

    console.log('Received direct upload request for URL:', url);
    console.log('Using optimized upload:', useOptimized);
    console.log('Using chunked upload:', useChunked);
    console.log('Chunk size:', (chunkSize / (1024 * 1024)).toFixed(2) + 'MB');

    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    let fileContentType: string;
    let fileName: string = 'unknown';
    let fileSize: number = 0;
    let fileStream: ReadableStream<Uint8Array> | null = null;

    if (contentType.includes('multipart/form-data')) {
      // Handle FormData request
      console.log('Received multipart/form-data request');
      const formData = await request.formData();
      const file = formData.get('file') as File;

      if (!file) {
        return NextResponse.json(
          { error: 'Missing file in FormData' },
          { status: 400 }
        );
      }

      // Get file metadata
      fileName = file.name;
      fileSize = file.size;
      fileContentType = file.type;

      // Get the file as a stream instead of loading it all into memory
      fileStream = file.stream();

      console.log(`Processing file: ${fileName}, Size: ${(fileSize / (1024 * 1024)).toFixed(2)}MB, Type: ${fileContentType}`);
    } else {
      // Get the file content directly from the request body as a stream
      fileStream = request.body;
      fileContentType = contentType;
      fileSize = parseInt(request.headers.get('content-length') || '0');
      console.log(`Processing raw data: Size: ${(fileSize / (1024 * 1024)).toFixed(2)}MB, Type: ${fileContentType}`);
    }

    if (!fileStream || fileSize === 0) {
      return NextResponse.json(
        { error: 'Empty file content or missing stream' },
        { status: 400 }
      );
    }

    // Create a custom HTTPS agent that ignores SSL certificate issues
    const httpsAgent = new https.Agent({
      rejectUnauthorized: false,
      keepAlive: true, // Enable keep-alive for better performance
      keepAliveMsecs: 1000, // Keep-alive for 1 second
      maxSockets: 50, // Allow more concurrent connections
      maxFreeSockets: 10, // Keep some sockets free
      timeout: 60000 // 60 second timeout
    });

    // Determine file type and destination
    const isCloudFrontUrl = url.includes('cloudfront.net');
    const isS3Url = url.includes('amazonaws.com');

    // Categorize file by size
    const isSmallFile = fileSize <= LARGE_FILE_THRESHOLD;
    const isMediumFile = fileSize > LARGE_FILE_THRESHOLD && fileSize <= MEDIUM_FILE_THRESHOLD;
    const isLargeFile = fileSize > MEDIUM_FILE_THRESHOLD;

    // Determine file size category for logging
    let fileSizeCategory = isSmallFile ? 'small' : (isMediumFile ? 'medium' : 'large');

    console.log('Is CloudFront URL:', isCloudFrontUrl);
    console.log('Is S3 URL:', isS3Url);
    console.log('File size category:', fileSizeCategory);
    console.log('File size:', (fileSize / (1024 * 1024)).toFixed(2) + 'MB');

    // Set appropriate headers
    const headers: Record<string, string> = {
      'Content-Type': fileContentType,
      'Content-Length': fileSize.toString(),
      'Connection': 'keep-alive',
      'TCP_NODELAY': '1',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    };

    // Add specific headers for CloudFront if needed
    if (isCloudFrontUrl) {
      const origin = process.env.NEXT_PUBLIC_FRONTEND_URL || 'https://wedzat.com';

      headers['Origin'] = origin;
      headers['X-Forwarded-Host'] = origin.replace(/^https?:\/\//, '');
    }

    // Start timing the upload
    const startTime = Date.now();

    // Choose the best upload strategy based on file size
    if (useOptimized) {
      console.log(`Using optimized upload strategy for ${fileSizeCategory} file (${(fileSize / (1024 * 1024)).toFixed(2)}MB).`);

      try {
        // Configure upload settings based on file size
        let uploadConfig: any = {
          headers,
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
          httpsAgent,
          decompress: false, // Disable automatic decompression for all file sizes
          // Use a 30-second timeout for connection establishment
          httpAgent: new https.Agent({
            keepAlive: true,
            timeout: 30000,
            maxSockets: 50
          }),
          // Disable transforming the request data
          transformRequest: [(data: any) => data],
          // Set a high timeout
          timeout: isLargeFile ? 3600000 : (isMediumFile ? 1800000 : 300000),
          // Headers are already set above
          // Track upload progress
          onUploadProgress: (progressEvent: any) => {
            if (progressEvent.total) {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              const elapsedSeconds = (Date.now() - startTime) / 1000;
              const uploadSpeed = elapsedSeconds > 0 ? (progressEvent.loaded / elapsedSeconds / (1024 * 1024)).toFixed(2) : 0;
              console.log(`Upload progress: ${percentCompleted}% at ${uploadSpeed} MB/s`);
            }
          }
        };

        // Optimize settings based on file size
        if (isSmallFile) {
          // Small file optimizations
          uploadConfig.timeout = 120000; // 2 minutes timeout
          uploadConfig.responseType = 'arraybuffer'; // Use arraybuffer for small files

          // Add TCP_NODELAY for small files to reduce latency
          headers['Connection'] = 'keep-alive';
          headers['TCP_NODELAY'] = '1';

          console.log('Using small file optimizations');
        } else if (isMediumFile) {
          // Medium file optimizations
          uploadConfig.timeout = 600000; // 10 minutes timeout
          uploadConfig.responseType = 'stream'; // Use streaming for medium files

          // Add optimizations for medium files
          headers['Connection'] = 'keep-alive';

          console.log('Using medium file optimizations');
        } else {
          // Large file optimizations
          uploadConfig.timeout = 3600000; // 1 hour timeout
          uploadConfig.responseType = 'stream'; // Use streaming for large files

          // Add optimizations for large files
          headers['Expect'] = '100-continue'; // Use 100-continue for large files

          // Use chunked upload if requested
          if (useChunked) {
            console.log(`Using chunked upload with ${(chunkSize / (1024 * 1024)).toFixed(2)}MB chunks`);

            // For very large files, we should implement true multipart upload
            // This is a simplified implementation that still uploads the whole file
            // In a production environment, you would implement S3's multipart upload API

            // Add chunked upload headers if needed
            headers['X-Chunked-Upload'] = 'true';
            headers['X-Chunk-Size'] = chunkSize.toString();
          } else {
            console.log('Using large file optimizations without chunking');
          }
        }

        // Execute the optimized upload
        console.log(`Starting optimized ${fileSizeCategory} file upload...`);

        // Create a readable stream from the file stream
        const response = await axios.put(url, fileStream, uploadConfig);

        const endTime = Date.now();
        const elapsedSeconds = (endTime - startTime) / 1000;
        const uploadSpeed = (fileSize / elapsedSeconds / (1024 * 1024)).toFixed(2);

        console.log(`Upload successful in ${elapsedSeconds.toFixed(2)}s (${uploadSpeed} MB/s)`);

        return NextResponse.json({
          success: true,
          status: response.status,
          method: `optimized_${fileSizeCategory}`,
          fileSize: fileSize,
          elapsedSeconds: elapsedSeconds,
          uploadSpeed: `${uploadSpeed} MB/s`,
          fileSizeCategory: fileSizeCategory
        });
      } catch (optimizedError: any) {
        console.error(`Optimized ${fileSizeCategory} file upload failed:`, optimizedError.message || 'Unknown error');

        // Fall back to standard upload
        console.log('Falling back to standard upload method');

        // Try standard upload as fallback with appropriate timeout
        const fallbackTimeout = isLargeFile ? 3600000 : (isMediumFile ? 1800000 : 300000);

        const response = await axios.put(url, fileStream, {
          headers,
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
          httpsAgent,
          timeout: fallbackTimeout
        });

        const endTime = Date.now();
        const elapsedSeconds = (endTime - startTime) / 1000;
        const uploadSpeed = (fileSize / elapsedSeconds / (1024 * 1024)).toFixed(2);

        console.log(`Fallback upload successful in ${elapsedSeconds.toFixed(2)}s (${uploadSpeed} MB/s)`);

        return NextResponse.json({
          success: true,
          status: response.status,
          method: 'fallback',
          fileSize: fileSize,
          elapsedSeconds: elapsedSeconds,
          uploadSpeed: `${uploadSpeed} MB/s`,
          fileSizeCategory: fileSizeCategory
        });
      }
    } else {
      // Standard upload (non-optimized)
      console.log(`Using standard upload method for ${fileSizeCategory} file`);

      try {
        // Set appropriate timeout based on file size
        const standardTimeout = isLargeFile ? 3600000 : (isMediumFile ? 1800000 : 300000);

        const response = await axios.put(url, fileStream, {
          headers,
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
          httpsAgent,
          timeout: standardTimeout
        });

        const endTime = Date.now();
        const elapsedSeconds = (endTime - startTime) / 1000;
        const uploadSpeed = (fileSize / elapsedSeconds / (1024 * 1024)).toFixed(2);

        console.log(`Standard upload successful in ${elapsedSeconds.toFixed(2)}s (${uploadSpeed} MB/s)`);

        return NextResponse.json({
          success: true,
          status: response.status,
          method: 'standard',
          fileSize: fileSize,
          elapsedSeconds: elapsedSeconds,
          uploadSpeed: `${uploadSpeed} MB/s`,
          fileSizeCategory: fileSizeCategory
        });
      } catch (standardError: any) {
        console.error('Standard upload failed:', standardError.message || 'Unknown error');

        // If CloudFront URL, try alternative approach
        if (isCloudFrontUrl) {
          console.log('Trying alternative upload method for CloudFront...');

          // Try with different headers
          const altHeaders: Record<string, string> = {
            'Content-Type': fileContentType,
            'Content-Length': fileSize.toString(),
          };

          // Set appropriate timeout based on file size
          const altTimeout = isLargeFile ? 3600000 : (isMediumFile ? 1800000 : 300000);

          const altResponse = await axios.put(url, fileStream, {
            headers: altHeaders,
            maxBodyLength: Infinity,
            maxContentLength: Infinity,
            httpsAgent,
            timeout: altTimeout
          });

          const endTime = Date.now();
          const elapsedSeconds = (endTime - startTime) / 1000;
          const uploadSpeed = (fileSize / elapsedSeconds / (1024 * 1024)).toFixed(2);

          console.log(`Alternative upload successful in ${elapsedSeconds.toFixed(2)}s (${uploadSpeed} MB/s)`);

          return NextResponse.json({
            success: true,
            status: altResponse.status,
            method: 'alternative',
            fileSize: fileSize,
            elapsedSeconds: elapsedSeconds,
            uploadSpeed: `${uploadSpeed} MB/s`,
            fileSizeCategory: fileSizeCategory
          });
        } else {
          throw standardError; // Re-throw the error if not CloudFront
        }
      }
    }
  } catch (error: any) {
    console.error('Error in upload proxy:', error);

    let errorMessage = 'Failed to upload file';
    let statusCode = 500;

    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
      console.error('Response data:', error.response.data);

      statusCode = error.response.status;
      errorMessage = `Upload service responded with status ${error.response.status}`;

      // Add more detailed error information for debugging
      if (error.response.data) {
        try {
          const dataStr = typeof error.response.data === 'object'
            ? JSON.stringify(error.response.data)
            : String(error.response.data);
          errorMessage += ` - ${dataStr}`;
        } catch (e) {
          console.error('Error stringifying response data:', e);
        }
      }
    } else if (error.request) {
      errorMessage = 'No response received from upload service';
    } else {
      errorMessage = error.message || 'Unknown error occurred';
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}