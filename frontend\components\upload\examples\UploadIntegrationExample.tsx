// components/upload/examples/UploadIntegrationExample.tsx
'use client';

import React from 'react';
import UploadButton from '../UploadButton';
import { useUploadModal, useUploadProgress } from '../../../contexts/SimplifiedUploadContext';

/**
 * Example component showing how to integrate the new upload system
 * This demonstrates various ways to trigger uploads and monitor progress
 */
const UploadIntegrationExample: React.FC = () => {
  const { open, close, isOpen, contentType } = useUploadModal();
  const { status, progress, error } = useUploadProgress();

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold">Upload System Integration Example</h2>
      
      {/* Basic Upload Buttons */}
      <section className="space-y-4">
        <h3 className="text-lg font-semibold">Basic Upload Buttons</h3>
        <div className="flex gap-4">
          <UploadButton />
          <UploadButton contentType="moments" />
          <UploadButton contentType="photos" />
          <UploadButton contentType="videos" />
        </div>
      </section>

      {/* Different Button Variants */}
      <section className="space-y-4">
        <h3 className="text-lg font-semibold">Button Variants</h3>
        <div className="flex gap-4 items-center">
          <UploadButton variant="default" contentType="photos">
            Upload Photo
          </UploadButton>
          <UploadButton variant="icon" contentType="moments" />
          <UploadButton variant="text" contentType="videos">
            Upload Video
          </UploadButton>
        </div>
      </section>

      {/* Programmatic Control */}
      <section className="space-y-4">
        <h3 className="text-lg font-semibold">Programmatic Control</h3>
        <div className="flex gap-4">
          <button
            onClick={() => open('moments')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Open Moments Upload
          </button>
          <button
            onClick={() => open('photos')}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Open Photo Upload
          </button>
          <button
            onClick={() => open('videos')}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Open Video Upload
          </button>
          {isOpen && (
            <button
              onClick={close}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Close Modal
            </button>
          )}
        </div>
      </section>

      {/* Upload Status Display */}
      <section className="space-y-4">
        <h3 className="text-lg font-semibold">Upload Status</h3>
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Modal Open:</strong> {isOpen ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Content Type:</strong> {contentType || 'None'}
            </div>
            <div>
              <strong>Upload Status:</strong> {status}
            </div>
            <div>
              <strong>Progress:</strong> {progress}%
            </div>
          </div>
          {error && (
            <div className="mt-2 text-red-600">
              <strong>Error:</strong> {error}
            </div>
          )}
          {status === 'uploading' && (
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Custom Upload Triggers */}
      <section className="space-y-4">
        <h3 className="text-lg font-semibold">Custom Upload Triggers</h3>
        <div className="grid grid-cols-3 gap-4">
          {/* Moments Card */}
          <div className="border rounded-lg p-4 text-center">
            <div className="text-4xl mb-2">📸</div>
            <h4 className="font-medium mb-2">Share a Moment</h4>
            <p className="text-sm text-gray-600 mb-4">
              Quick photo or video story
            </p>
            <UploadButton 
              contentType="moments" 
              className="w-full"
            >
              Upload Moment
            </UploadButton>
          </div>

          {/* Photos Card */}
          <div className="border rounded-lg p-4 text-center">
            <div className="text-4xl mb-2">🖼️</div>
            <h4 className="font-medium mb-2">Upload Photo</h4>
            <p className="text-sm text-gray-600 mb-4">
              Share your wedding photos
            </p>
            <UploadButton 
              contentType="photos" 
              className="w-full"
            >
              Upload Photo
            </UploadButton>
          </div>

          {/* Videos Card */}
          <div className="border rounded-lg p-4 text-center">
            <div className="text-4xl mb-2">🎥</div>
            <h4 className="font-medium mb-2">Upload Video</h4>
            <p className="text-sm text-gray-600 mb-4">
              Share wedding videos with details
            </p>
            <UploadButton 
              contentType="videos" 
              className="w-full"
            >
              Upload Video
            </UploadButton>
          </div>
        </div>
      </section>

      {/* Integration Notes */}
      <section className="space-y-4">
        <h3 className="text-lg font-semibold">Integration Notes</h3>
        <div className="bg-blue-50 p-4 rounded-lg text-sm">
          <ul className="space-y-2">
            <li>• Upload buttons can be placed anywhere in your app</li>
            <li>• Modal state is managed globally through context</li>
            <li>• Progress tracking works automatically for all upload types</li>
            <li>• Each content type has its own validation and requirements</li>
            <li>• No localhost dependencies - uses environment variables</li>
            <li>• TypeScript support for all components and data handlers</li>
          </ul>
        </div>
      </section>
    </div>
  );
};

export default UploadIntegrationExample;
