// utils/__tests__/uploadDataHandlers.test.ts
/**
 * Tests for upload data handlers
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PhotoDataHandler, VideoDataHandler } from '../uploadDataHandlers';

// Mock File constructor for testing
class MockFile {
  name: string;
  size: number;
  type: string;

  constructor(name: string, size: number, type: string) {
    this.name = name;
    this.size = size;
    this.type = type;
  }
}

describe('MomentsDataHandler', () => {
  const mockFile = new MockFile('test-moment.jpg', 1024 * 1024, 'image/jpeg') as unknown as File;

  test('validates required fields', () => {
    const result = MomentsDataHandler.validate({});
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('File is required');
    expect(result.errors).toContain('Media type is required');
  });

  test('validates video duration requirement', () => {
    const result = MomentsDataHandler.validate({
      file: mockFile,
      mediaType: 'video'
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Video duration is required');
  });

  test('validates file size limit', () => {
    const largeFile = new MockFile('large.jpg', 6000 * 1024 * 1024, 'image/jpeg') as unknown as File;
    const result = MomentsDataHandler.validate({
      file: largeFile,
      mediaType: 'photo'
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('File size exceeds maximum limit of 5GB');
  });

  test('prepares valid upload data', () => {
    const data = MomentsDataHandler.prepareUploadData({
      file: mockFile,
      mediaType: 'photo',
      title: 'Test Moment'
    });

    expect(data.file).toBe(mockFile);
    expect(data.mediaType).toBe('photo');
    expect(data.mediaSubtype).toBe('story');
    expect(data.isStory).toBe(true);
    expect(data.title).toBe('Test Moment');
  });

  test('generates correct API payload', () => {
    const uploadData = MomentsDataHandler.prepareUploadData({
      file: mockFile,
      mediaType: 'photo'
    });
    const payload = MomentsDataHandler.getApiPayload(uploadData);

    expect(payload.file).toBe(mockFile);
    expect(payload.mediaType).toBe('photo');
    expect(payload.category).toBe('story');
    expect(payload.description).toBe('');
    expect(payload.tags).toEqual([]);
    expect(payload.details).toEqual({ is_story: true });
  });
});

describe('PhotoDataHandler', () => {
  const mockFile = new MockFile('test-photo.jpg', 1024 * 1024, 'image/jpeg') as unknown as File;

  test('validates required fields', () => {
    const result = PhotoDataHandler.validate({
      file: mockFile
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Caption is required');
    expect(result.errors).toContain('Place is required');
    expect(result.errors).toContain('Event type is required');
  });

  test('validates file type', () => {
    const videoFile = new MockFile('test.mp4', 1024 * 1024, 'video/mp4') as unknown as File;
    const result = PhotoDataHandler.validate({
      file: videoFile,
      caption: 'Test',
      place: 'Test Place',
      eventType: 'wedding'
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('File must be an image');
  });

  test('prepares valid upload data', () => {
    const data = PhotoDataHandler.prepareUploadData({
      file: mockFile,
      caption: 'Beautiful wedding photo',
      place: 'Mumbai',
      eventType: 'wedding',
      title: 'Wedding Photo'
    });

    expect(data.file).toBe(mockFile);
    expect(data.mediaType).toBe('photo');
    expect(data.mediaSubtype).toBe('post');
    expect(data.caption).toBe('Beautiful wedding photo');
    expect(data.place).toBe('Mumbai');
    expect(data.eventType).toBe('wedding');
  });
});

describe('VideoDataHandler', () => {
  const mockFile = new MockFile('test-video.mp4', 100 * 1024 * 1024, 'video/mp4') as unknown as File;

  test('validates required fields', () => {
    const result = VideoDataHandler.validate({
      file: mockFile
    });
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(5); // Multiple required fields
  });

  test('validates vendor details for my_wedding category', () => {
    const result = VideoDataHandler.validate({
      file: mockFile,
      caption: 'Test',
      place: 'Test Place',
      partner: 'Partner',
      budget: 'under_5_lakhs',
      weddingStyle: 'Traditional',
      eventType: 'wedding',
      videoCategory: 'my_wedding',
      duration: 30,
      vendorDetails: {
        venue: { name: 'Test Venue', mobileNumber: '1234567890' }
        // Missing required vendors
      }
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('photographer vendor details are required for my_wedding category');
  });

  test('determines correct media subtype based on duration', () => {
    // Test flash (≤15 seconds)
    const flashData = VideoDataHandler.prepareUploadData({
      file: mockFile,
      caption: 'Test',
      place: 'Test Place',
      partner: 'Partner',
      budget: 'under_5_lakhs',
      weddingStyle: 'Traditional',
      eventType: 'wedding',
      videoCategory: 'wedding_vlog',
      duration: 10,
      vendorDetails: {
        venue: { name: 'Test Venue', mobileNumber: '1234567890' }
      }
    });
    expect(flashData.mediaSubtype).toBe('flash');

    // Test glimpse (≤60 seconds)
    const glimpseData = VideoDataHandler.prepareUploadData({
      file: mockFile,
      caption: 'Test',
      place: 'Test Place',
      partner: 'Partner',
      budget: 'under_5_lakhs',
      weddingStyle: 'Traditional',
      eventType: 'wedding',
      videoCategory: 'wedding_vlog',
      duration: 45,
      vendorDetails: {
        venue: { name: 'Test Venue', mobileNumber: '1234567890' }
      }
    });
    expect(glimpseData.mediaSubtype).toBe('glimpse');

    // Test movie (>60 seconds)
    const movieData = VideoDataHandler.prepareUploadData({
      file: mockFile,
      caption: 'Test',
      place: 'Test Place',
      partner: 'Partner',
      budget: 'under_5_lakhs',
      weddingStyle: 'Traditional',
      eventType: 'wedding',
      videoCategory: 'wedding_vlog',
      duration: 120,
      vendorDetails: {
        venue: { name: 'Test Venue', mobileNumber: '1234567890' }
      }
    });
    expect(movieData.mediaSubtype).toBe('movie');
  });
});
