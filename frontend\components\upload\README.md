# Simplified Upload System

This is a refactored, simplified upload system that provides clean separation of concerns and better maintainability.

## Architecture

### Data Handlers (`utils/uploadDataHandlers.ts`)
- **MomentsDataHandler**: Handles moments (photo/video stories) with minimal data requirements
- **PhotoDataHandler**: <PERSON>les photo uploads with caption, place, event type
- **VideoDataHandler**: Handles video uploads with full metadata and vendor details

### Upload Services (`services/uploadServices.ts`)
- **MomentsUploadService**: Dedicated service for moments uploads
- **PhotoUploadService**: Dedicated service for photo uploads  
- **VideoUploadService**: Dedicated service for video uploads
- **uploadContent()**: Unified function that routes to appropriate service

### Components
- **SimplifiedUploadManager**: Main upload component with clean phase management
- **UploadButton**: Reusable button component for triggering uploads
- **UploadModal**: Modal wrapper for the upload manager
- **UploadProvider**: Context provider that wraps the app

### Context (`contexts/SimplifiedUploadContext.tsx`)
- Simplified state management
- Hooks for modal, progress, and file management
- No complex conditional logic

## Usage

### 1. Wrap your app with UploadProvider

```tsx
// In your main layout or _app.tsx
import UploadProvider from './components/upload/UploadProvider';

export default function Layout({ children }) {
  return (
    <UploadProvider>
      {children}
    </UploadProvider>
  );
}
```

### 2. Use UploadButton anywhere in your app

```tsx
import UploadButton from './components/upload/UploadButton';

// Generic upload button
<UploadButton />

// Specific content type buttons
<UploadButton contentType="moments" />
<UploadButton contentType="photos" />
<UploadButton contentType="videos" />

// Different variants
<UploadButton variant="icon" contentType="moments" />
<UploadButton variant="text" contentType="photos" />
```

### 3. Programmatic upload modal control

```tsx
import { useUploadModal } from './contexts/SimplifiedUploadContext';

function MyComponent() {
  const { open, close, isOpen } = useUploadModal();
  
  const handleUpload = () => {
    open('photos'); // Opens modal for photo upload
  };
  
  return (
    <button onClick={handleUpload}>
      Upload Photo
    </button>
  );
}
```

## Key Improvements

1. **Separation of Concerns**: Data validation, API calls, and UI are clearly separated
2. **Type Safety**: Strong TypeScript types for each upload type
3. **No Localhost Dependencies**: All hardcoded localhost references removed
4. **Simplified State Management**: Clean, predictable state transitions
5. **Reusable Components**: Upload functionality can be triggered from anywhere
6. **Consistent API**: Same patterns for all upload types
7. **Better Error Handling**: Validation happens before upload attempts
8. **Progress Tracking**: Built-in progress tracking for all upload types

## Upload Flow

1. **Type Selection**: User selects content type (moments/photos/videos)
2. **File Selection**: User selects file with appropriate type validation
3. **Data Entry**: Form fields based on content type requirements
4. **Validation**: Client-side validation before upload
5. **Upload**: Progress tracking with appropriate service
6. **Completion**: Success/error handling and cleanup

## Content Type Requirements

### Moments
- File (photo or video)
- Title (optional, defaults to filename)
- Duration (for videos)
- Face verification (handled separately)

### Photos  
- File (image)
- Title
- Caption
- Place
- Event Type

### Videos
- File (video)
- Title
- Caption
- Place
- Partner
- Budget
- Wedding Style
- Event Type
- Video Category (my_wedding/wedding_vlog)
- Vendor Details (varies by category)
- Duration (auto-detected)
- Thumbnail (optional)

## Migration from Old System

The old complex UploadManager can be gradually replaced:

1. Replace UploadManager imports with SimplifiedUploadManager
2. Update context usage to use SimplifiedUploadContext
3. Replace complex upload logic with new services
4. Remove localhost references
5. Update any custom upload buttons to use UploadButton component
