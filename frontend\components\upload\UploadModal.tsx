// components/upload/UploadModal.tsx
'use client';

import React from 'react';
import { useUploadModal } from '../../contexts/SimplifiedUploadContext';
import SimplifiedUploadManager from './SimplifiedUploadManager';

const UploadModal: React.FC = () => {
  const { isOpen, close } = useUploadModal();

  if (!isOpen) return null;

  return (
    <SimplifiedUploadManager 
      onClose={close}
      onUploadComplete={() => {
        // Handle upload completion
        console.log('Upload completed successfully');
        // You can add additional logic here like refreshing data
      }}
    />
  );
};

export default UploadModal;
