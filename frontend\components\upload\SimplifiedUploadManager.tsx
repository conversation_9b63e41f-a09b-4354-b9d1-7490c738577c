// components/upload/SimplifiedUploadManager.tsx
'use client';

import React, { useState, useRef } from 'react';
import { X } from 'lucide-react';
import { uploadContent, UploadProgress, UploadResult } from '../../services/uploadServices';
import { MomentsUploadData, PhotoUploadData, VideoUploadData } from '../../utils/uploadDataHandlers';
import { getVideoDuration } from '../../utils/uploadUtils';
import { showSuccessAlert, showErrorAlert } from '../../utils/alertUtils';

export type ContentType = 'moments' | 'photos' | 'videos';
export type UploadPhase = 'typeSelection' | 'fileUpload' | 'dataEntry' | 'uploading' | 'complete';

interface SimplifiedUploadManagerProps {
  onClose?: () => void;
  onUploadComplete?: () => void;
}

interface UploadState {
  phase: UploadPhase;
  contentType: ContentType | null;
  file: File | null;
  mediaType: 'photo' | 'video' | null;
  uploadProgress: number;
  isUploading: boolean;
}

const SimplifiedUploadManager: React.FC<SimplifiedUploadManagerProps> = ({ 
  onClose, 
  onUploadComplete 
}) => {
  const [state, setState] = useState<UploadState>({
    phase: 'typeSelection',
    contentType: null,
    file: null,
    mediaType: null,
    uploadProgress: 0,
    isUploading: false
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleTypeSelection = (type: ContentType) => {
    setState(prev => ({
      ...prev,
      contentType: type,
      phase: 'fileUpload'
    }));
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const mediaType = file.type.startsWith('image/') ? 'photo' : 'video';
    
    setState(prev => ({
      ...prev,
      file,
      mediaType,
      phase: 'dataEntry'
    }));
  };

  const handleMomentsUpload = async (formData: FormData) => {
    if (!state.file) return;

    setState(prev => ({ ...prev, isUploading: true, phase: 'uploading' }));

    try {
      let duration: number | undefined;
      if (state.mediaType === 'video') {
        duration = await getVideoDuration(state.file);
      }

      const uploadData: Partial<MomentsUploadData> = {
        file: state.file,
        mediaType: state.mediaType!,
        title: formData.get('title') as string || state.file.name,
        duration
      };

      const result = await uploadContent('moments', uploadData, (progress) => {
        setState(prev => ({ ...prev, uploadProgress: progress.percentage }));
      });

      if (result.success) {
        showSuccessAlert('Upload Successful', 'Your moment has been uploaded successfully!');
        setState(prev => ({ ...prev, phase: 'complete' }));
        onUploadComplete?.();
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error: any) {
      console.error('Moments upload failed:', error);
      showErrorAlert('Upload Failed', error.message || 'Failed to upload moment');
      setState(prev => ({ ...prev, isUploading: false, phase: 'dataEntry' }));
    }
  };

  const handlePhotoUpload = async (formData: FormData) => {
    if (!state.file) return;

    setState(prev => ({ ...prev, isUploading: true, phase: 'uploading' }));

    try {
      const uploadData: Partial<PhotoUploadData> = {
        file: state.file,
        mediaType: 'photo',
        title: formData.get('title') as string,
        caption: formData.get('caption') as string,
        place: formData.get('place') as string,
        eventType: formData.get('eventType') as string
      };

      const result = await uploadContent('photos', uploadData, (progress) => {
        setState(prev => ({ ...prev, uploadProgress: progress.percentage }));
      });

      if (result.success) {
        showSuccessAlert('Upload Successful', 'Your photo has been uploaded successfully!');
        setState(prev => ({ ...prev, phase: 'complete' }));
        onUploadComplete?.();
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error: any) {
      console.error('Photo upload failed:', error);
      showErrorAlert('Upload Failed', error.message || 'Failed to upload photo');
      setState(prev => ({ ...prev, isUploading: false, phase: 'dataEntry' }));
    }
  };

  const handleVideoUpload = async (formData: FormData) => {
    if (!state.file) return;

    setState(prev => ({ ...prev, isUploading: true, phase: 'uploading' }));

    try {
      const duration = await getVideoDuration(state.file);
      
      // Parse vendor details from form data
      const vendorDetails: Record<string, { name: string; mobileNumber: string }> = {};
      const vendorTypes = ['venue', 'photographer', 'makeup_artist', 'decoration', 'caterer'];
      
      vendorTypes.forEach(type => {
        const name = formData.get(`vendor_${type}_name`) as string;
        const mobile = formData.get(`vendor_${type}_mobile`) as string;
        if (name && mobile) {
          vendorDetails[type] = { name, mobileNumber: mobile };
        }
      });

      const uploadData: Partial<VideoUploadData> = {
        file: state.file,
        mediaType: 'video',
        title: formData.get('title') as string,
        caption: formData.get('caption') as string,
        place: formData.get('place') as string,
        partner: formData.get('partner') as string,
        budget: formData.get('budget') as string,
        weddingStyle: formData.get('weddingStyle') as string,
        eventType: formData.get('eventType') as string,
        videoCategory: formData.get('videoCategory') as 'my_wedding' | 'wedding_vlog',
        vendorDetails,
        duration
      };

      const result = await uploadContent('videos', uploadData, (progress) => {
        setState(prev => ({ ...prev, uploadProgress: progress.percentage }));
      });

      if (result.success) {
        showSuccessAlert('Upload Successful', 'Your video has been uploaded successfully!');
        setState(prev => ({ ...prev, phase: 'complete' }));
        onUploadComplete?.();
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error: any) {
      console.error('Video upload failed:', error);
      showErrorAlert('Upload Failed', error.message || 'Failed to upload video');
      setState(prev => ({ ...prev, isUploading: false, phase: 'dataEntry' }));
    }
  };

  const handleFormSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);

    switch (state.contentType) {
      case 'moments':
        await handleMomentsUpload(formData);
        break;
      case 'photos':
        await handlePhotoUpload(formData);
        break;
      case 'videos':
        await handleVideoUpload(formData);
        break;
    }
  };

  const resetUpload = () => {
    setState({
      phase: 'typeSelection',
      contentType: null,
      file: null,
      mediaType: null,
      uploadProgress: 0,
      isUploading: false
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClose = () => {
    resetUpload();
    onClose?.();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Upload Content</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Type Selection */}
        {state.phase === 'typeSelection' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Select Content Type</h3>
            <div className="grid grid-cols-3 gap-4">
              <button
                onClick={() => handleTypeSelection('moments')}
                className="p-4 border rounded-lg hover:border-red-500 text-center"
              >
                <div className="text-2xl mb-2">📸</div>
                <div>Moments</div>
              </button>
              <button
                onClick={() => handleTypeSelection('photos')}
                className="p-4 border rounded-lg hover:border-red-500 text-center"
              >
                <div className="text-2xl mb-2">🖼️</div>
                <div>Photos</div>
              </button>
              <button
                onClick={() => handleTypeSelection('videos')}
                className="p-4 border rounded-lg hover:border-red-500 text-center"
              >
                <div className="text-2xl mb-2">🎥</div>
                <div>Videos</div>
              </button>
            </div>
          </div>
        )}

        {/* File Upload */}
        {state.phase === 'fileUpload' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Select File</h3>
            <input
              ref={fileInputRef}
              type="file"
              accept={state.contentType === 'photos' ? 'image/*' : state.contentType === 'videos' ? 'video/*' : 'image/*,video/*'}
              onChange={handleFileSelect}
              className="w-full p-2 border rounded"
            />
            <button
              onClick={() => setState(prev => ({ ...prev, phase: 'typeSelection' }))}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Back
            </button>
          </div>
        )}

        {/* Data Entry Forms */}
        {state.phase === 'dataEntry' && (
          <form onSubmit={handleFormSubmit} className="space-y-4">
            <h3 className="text-lg font-medium">
              {state.contentType === 'moments' && 'Moment Details'}
              {state.contentType === 'photos' && 'Photo Details'}
              {state.contentType === 'videos' && 'Video Details'}
            </h3>

            {/* File Preview */}
            <div className="mb-4">
              <p className="text-sm text-gray-600">Selected file: {state.file?.name}</p>
            </div>

            {/* Common Fields */}
            <div>
              <label className="block text-sm font-medium mb-1">Title</label>
              <input
                name="title"
                type="text"
                defaultValue={state.file?.name.replace(/\.[^/.]+$/, "")}
                className="w-full p-2 border rounded"
                required={state.contentType !== 'moments'}
              />
            </div>

            {/* Moments-specific fields (minimal) */}
            {state.contentType === 'moments' && (
              <p className="text-sm text-gray-600">
                Moments only require file upload and face verification.
              </p>
            )}

            {/* Photo-specific fields */}
            {state.contentType === 'photos' && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-1">Caption</label>
                  <textarea
                    name="caption"
                    className="w-full p-2 border rounded"
                    rows={3}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Place</label>
                  <input
                    name="place"
                    type="text"
                    className="w-full p-2 border rounded"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Event Type</label>
                  <select name="eventType" className="w-full p-2 border rounded" required>
                    <option value="">Select Event Type</option>
                    <option value="engagement">Engagement</option>
                    <option value="wedding">Wedding</option>
                    <option value="reception">Reception</option>
                    <option value="pre_wedding">Pre Wedding</option>
                  </select>
                </div>
              </>
            )}

            {/* Video-specific fields */}
            {state.contentType === 'videos' && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-1">Caption</label>
                  <textarea
                    name="caption"
                    className="w-full p-2 border rounded"
                    rows={3}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Place</label>
                    <input
                      name="place"
                      type="text"
                      className="w-full p-2 border rounded"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Partner</label>
                    <input
                      name="partner"
                      type="text"
                      className="w-full p-2 border rounded"
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Budget</label>
                    <select name="budget" className="w-full p-2 border rounded" required>
                      <option value="">Select Budget</option>
                      <option value="under_5_lakhs">Under 5 Lakhs</option>
                      <option value="5_to_10_lakhs">5-10 Lakhs</option>
                      <option value="10_to_20_lakhs">10-20 Lakhs</option>
                      <option value="above_20_lakhs">Above 20 Lakhs</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Wedding Style</label>
                    <input
                      name="weddingStyle"
                      type="text"
                      className="w-full p-2 border rounded"
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Event Type</label>
                    <select name="eventType" className="w-full p-2 border rounded" required>
                      <option value="">Select Event Type</option>
                      <option value="engagement">Engagement</option>
                      <option value="wedding">Wedding</option>
                      <option value="reception">Reception</option>
                      <option value="pre_wedding">Pre Wedding</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Video Category</label>
                    <select name="videoCategory" className="w-full p-2 border rounded" required>
                      <option value="">Select Category</option>
                      <option value="my_wedding">My Wedding Videos</option>
                      <option value="wedding_vlog">Wedding Vlogs</option>
                    </select>
                  </div>
                </div>

                {/* Vendor Details */}
                <div className="space-y-3">
                  <h4 className="font-medium">Vendor Details</h4>
                  {['venue', 'photographer', 'makeup_artist', 'decoration'].map((vendor) => (
                    <div key={vendor} className="grid grid-cols-2 gap-2">
                      <input
                        name={`vendor_${vendor}_name`}
                        type="text"
                        placeholder={`${vendor.replace('_', ' ')} name`}
                        className="p-2 border rounded text-sm"
                      />
                      <input
                        name={`vendor_${vendor}_mobile`}
                        type="tel"
                        placeholder={`${vendor.replace('_', ' ')} mobile`}
                        className="p-2 border rounded text-sm"
                      />
                    </div>
                  ))}
                </div>
              </>
            )}

            {/* Form Actions */}
            <div className="flex justify-between pt-4">
              <button
                type="button"
                onClick={() => setState(prev => ({ ...prev, phase: 'fileUpload' }))}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={state.isUploading}
                className="px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
              >
                Upload
              </button>
            </div>
          </form>
        )}

        {/* Upload Progress */}
        {state.phase === 'uploading' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Uploading...</h3>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-red-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${state.uploadProgress}%` }}
              />
            </div>
            <p className="text-center">{state.uploadProgress}%</p>
          </div>
        )}

        {/* Complete */}
        {state.phase === 'complete' && (
          <div className="text-center space-y-4">
            <div className="text-green-500 text-4xl">✅</div>
            <h3 className="text-lg font-medium">Upload Complete!</h3>
            <button
              onClick={handleClose}
              className="px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimplifiedUploadManager;
